---
type: "always_apply"
---

description: Laravel 12 Architectural Rules - Service & Repository Pattern

## 🧱 Project Structure

-   Use the following folder structure for business logic and domain separation:
    -   `app/Services`: For all service classes (business logic).
    -   `app/Repositories/Contracts`: For repository interfaces only.
    -   `app/Repositories/Eloquent`: For repository implementations.
    -   `app/Http/Controllers`: For thin controllers, only calling services.

---

## 🔁 Repository Pattern Rules

-   All repository interfaces must be named using the format: `XxxRepositoryInterface`.
-   All repository implementations must be named: `XxxRepository`.
-   Implementations must reside in:  
    `App\Repositories\Eloquent`
-   Interfaces must reside in:  
    `App\Repositories\Contracts`
-   Repository contracts should be type-hinted in the constructor of services, not directly used in controllers.
-   Never instantiate repositories directly.

---

## 🧠 Service Pattern Rules

-   All business logic must be placed inside the `app/Services` folder.
-   Service class names should follow the format: `XxxService`.
-   Do **not** define interfaces for service classes.
-   Services should be injected into controllers via constructor dependency injection.
-   Services may call repositories, but repositories should **never** call services.

---

## 🧼 Controller Rules

-   Controllers should be thin: only handle HTTP request/response, validation, and delegate logic to services.
-   Never use repositories or models directly inside controllers.
-   Always resolve services via constructor injection.

---

## 📦 Other Best Practices

-   Follow PSR-4 naming conventions and use namespaces consistently.
-   Avoid placing logic in models (`Fat Model` anti-pattern).
-   Use Traits sparingly and only in the `app/Traits` folder for shared behavior.
-   Exception classes should go in `app/Exceptions`, not mixed into services or controllers.
-   Follow Laravel 12 service provider binding patterns for interface resolution.

---
